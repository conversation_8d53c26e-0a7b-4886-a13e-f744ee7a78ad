import React, { useState } from 'react';
import { XStack, Text, Theme } from 'tamagui';
import { Button } from '../Button';
import { Ionicons } from '@expo/vector-icons';
import { Alert } from 'react-native';
import { GoogleAuthService } from '../../services/googleAuth';
import { apiService } from '../../services/api';
import { useCurrentUserData } from '../useCurrentUserData';
import { useRouter } from 'expo-router';
import { useTranslation } from 'react-i18next';

interface GoogleSignInButtonProps {
  onSuccess?: () => void;
  onError?: (error: string) => void;
  disabled?: boolean;
}

export const GoogleSignInButton: React.FC<GoogleSignInButtonProps> = ({
  onSuccess,
  onError,
  disabled = false,
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const { setCurrentUser } = useCurrentUserData();
  const router = useRouter();
  const { t } = useTranslation();

  const handleGoogleSignIn = async () => {
    if (isLoading || disabled) return;

    setIsLoading(true);

    try {
      // Sign in with Google
      const googleResult = await GoogleAuthService.signIn();

      if (!googleResult.success || !googleResult.user) {
        throw new Error(googleResult.error || 'Google sign-in failed');
      }

      // Send the Google ID token to our backend for verification and user creation/login
      const response = await apiService.googleAuth({
        idToken: googleResult.user.idToken,
        email: googleResult.user.email,
        displayName: googleResult.user.displayName,
        photoURL: googleResult.user.photoURL,
        googleId: googleResult.user.uid,
      });

      if (response.success && response.data) {
        // Set the current user
        setCurrentUser(response.data.user);

        // Show success message
        Alert.alert(
          t('auth.loginSuccess', { defaultValue: 'Login Successful' }),
          t('auth.welcomeBack', { 
            defaultValue: 'Welcome back, {{name}}!',
            name: response.data.user.firstName || response.data.user.displayName 
          })
        );

        // Redirect based on role
        if (response.data.user.role === 'customer') {
          router.push('/(customer-pages)/home');
        } else if (response.data.user.role === 'supplier') {
          router.push('/(supplier-pages)/home');
        }

        onSuccess?.();
      } else {
        throw new Error(response.message || 'Authentication failed');
      }
    } catch (error: any) {
      console.error('Google authentication error:', error);
      
      const errorMessage = error.message || t('auth.googleSignInFailed', { 
        defaultValue: 'Google sign-in failed. Please try again.' 
      });

      Alert.alert(
        t('auth.authenticationFailed', { defaultValue: 'Authentication Failed' }),
        errorMessage
      );

      onError?.(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Theme name="light">
      <Button
        backgroundColor="white"
        borderColor="$gray6"
        borderWidth={1}
        color="$gray12"
        hoverStyle={{ 
          backgroundColor: '$gray2',
          borderColor: '$gray7'
        }}
        pressStyle={{ 
          backgroundColor: '$gray3',
          borderColor: '$gray8'
        }}
        disabled={isLoading || disabled}
        opacity={isLoading || disabled ? 0.6 : 1}
        onPress={handleGoogleSignIn}
        height={56}
        fontSize="$5"
        fontWeight="600"
        width="100%"
      >
        <XStack alignItems="center" gap="$3">
          {isLoading ? (
            <Ionicons name="refresh" size={20} color="#666" />
          ) : (
            <Ionicons name="logo-google" size={20} color="#4285F4" />
          )}
          <Text color="$gray12" fontSize="$5" fontWeight="600">
            {isLoading 
              ? t('auth.signingIn', { defaultValue: 'Signing in...' })
              : t('auth.continueWithGoogle', { defaultValue: 'Continue with Google' })
            }
          </Text>
        </XStack>
      </Button>
    </Theme>
  );
};
