import { GoogleSignin } from '@react-native-google-signin/google-signin';
import { auth, googleProvider } from '../utils/firebase';
import { signInWithCredential, GoogleAuthProvider } from 'firebase/auth';

export interface GoogleAuthResult {
  success: boolean;
  user?: {
    uid: string;
    email: string;
    displayName: string;
    photoURL: string;
    idToken: string;
  };
  error?: string;
}

export class GoogleAuthService {
  private static isConfigured = false;

  static configure() {
    if (this.isConfigured) return;

    GoogleSignin.configure({
      webClientId: process.env.EXPO_PUBLIC_GOOGLE_WEB_CLIENT_ID,
      offlineAccess: true,
      hostedDomain: '',
      forceCodeForRefreshToken: true,
    });

    this.isConfigured = true;
  }

  static async signIn(): Promise<GoogleAuthResult> {
    try {
      this.configure();

      // Check if device supports Google Play Services
      await GoogleSignin.hasPlayServices();

      // Sign in with Google
      const userInfo = await GoogleSignin.signIn();
      
      if (!userInfo.data?.idToken) {
        throw new Error('No ID token received from Google');
      }

      // Create Firebase credential
      const googleCredential = GoogleAuthProvider.credential(userInfo.data.idToken);

      // Sign in with Firebase
      const firebaseResult = await signInWithCredential(auth, googleCredential);

      return {
        success: true,
        user: {
          uid: firebaseResult.user.uid,
          email: firebaseResult.user.email || '',
          displayName: firebaseResult.user.displayName || '',
          photoURL: firebaseResult.user.photoURL || '',
          idToken: userInfo.data.idToken,
        },
      };
    } catch (error: any) {
      console.error('Google Sign-In Error:', error);
      
      let errorMessage = 'Google sign-in failed';
      
      if (error.code === 'auth/account-exists-with-different-credential') {
        errorMessage = 'An account already exists with this email address';
      } else if (error.code === 'auth/invalid-credential') {
        errorMessage = 'Invalid Google credentials';
      } else if (error.code === 'auth/operation-not-allowed') {
        errorMessage = 'Google sign-in is not enabled';
      } else if (error.code === 'auth/user-disabled') {
        errorMessage = 'This account has been disabled';
      } else if (error.message) {
        errorMessage = error.message;
      }

      return {
        success: false,
        error: errorMessage,
      };
    }
  }

  static async signOut(): Promise<void> {
    try {
      await GoogleSignin.signOut();
      await auth.signOut();
    } catch (error) {
      console.error('Google Sign-Out Error:', error);
    }
  }

  static async isSignedIn(): Promise<boolean> {
    try {
      return await GoogleSignin.isSignedIn();
    } catch (error) {
      return false;
    }
  }

  static async getCurrentUser() {
    try {
      return await GoogleSignin.getCurrentUser();
    } catch (error) {
      return null;
    }
  }
}
