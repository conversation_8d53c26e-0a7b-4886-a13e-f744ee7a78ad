import React from 'react';
import { YStack, XStack, Text, H5 } from 'tamagui';
import { useSignupStore } from '../useSignupStore';
import { CustomTextField } from '../../CustomTextField';
import { GoogleSignInButton } from '../GoogleSignInButton';
import { Ionicons } from '@expo/vector-icons';
import { useTranslation } from 'react-i18next';

export const BasicInfoStep = () => {
  const { t } = useTranslation();
  const { signupData, updateSignupData } = useSignupStore();

  return (
    <YStack gap="$4" paddingTop="$2">
      <YStack gap="$2" alignItems="center" marginBottom="$4">
        <Ionicons name="person-add" size={48} color="#7529B3" />
        <H5 textAlign="center" color="$primary">{t('signup.basicInfo.title', { defaultValue: "Let's get to know you" })}</H5>
        <Text textAlign="center" color="$gray10" fontSize="$3">
          {t('signup.basicInfo.subtitle', { defaultValue: 'Please provide your basic information to get started' })}
        </Text>
      </YStack>

      <YStack gap="$4">
        <CustomTextField
          label={t('auth.firstName', { defaultValue: 'First Name' })}
          placeholder={t('auth.enterFirstName', { defaultValue: 'Enter your first name' })}
          value={signupData.firstName}
          onChangeText={(text) => updateSignupData({ firstName: text })}
          icon="person"
          autoCapitalize="words"
          required
        />

        <CustomTextField
          label={t('auth.lastName', { defaultValue: 'Last Name' })}
          placeholder={t('auth.enterLastName', { defaultValue: 'Enter your last name' })}
          value={signupData.lastName}
          onChangeText={(text) => updateSignupData({ lastName: text })}
          icon="person"
          autoCapitalize="words"
          required
        />

        <CustomTextField
          label={t('auth.emailAddress', { defaultValue: 'Email Address' })}
          placeholder={t('auth.enterEmailAddress', { defaultValue: 'Enter your email address' })}
          value={signupData.email}
          onChangeText={(text) => updateSignupData({ email: text.toLowerCase() })}
          icon="mail"
          keyboardType="email-address"
          autoCapitalize="none"
          required
        />
      </YStack>

      <YStack gap="$2" marginTop="$3">
        <Text fontSize="$2" color="$gray9" textAlign="center">
          {t('common.allFieldsRequired', { defaultValue: '* All fields are required' })}
        </Text>
      </YStack>
    </YStack>
  );
};
